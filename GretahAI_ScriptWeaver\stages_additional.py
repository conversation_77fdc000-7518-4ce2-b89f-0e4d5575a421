"""
Additional stage functions for the GretahAI ScriptWeaver application.
Each stage function handles a specific part of the application flow.
"""

import os
import logging
import streamlit as st
from pathlib import Path
from datetime import datetime
import time
import re
from core.ai import merge_scripts_with_ai, optimize_script_with_ai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.stages_additional")

def create_combined_script(state):
    """
    Create a combined script file containing all steps for the current test case.

    This should be called after all steps have been processed (when state.all_steps_done is True).
    The combined script will merge all individual step scripts in the correct order.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        str: Path to the combined script file, or None if creation failed
    """
    try:
        # Check if we have a test case and previous scripts
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            logger.warning("No selected test case found, cannot create combined script")
            return None

        if not hasattr(state, 'previous_scripts') or not state.previous_scripts:
            logger.warning("No previous scripts found, cannot create combined script")
            return None

        # Get the test case ID
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

        # Get all step numbers in order
        step_numbers = sorted(state.previous_scripts.keys(), key=int)

        if not step_numbers:
            logger.warning("No step scripts found, cannot create combined script")
            return None

        logger.info(f"Creating combined script for test case {test_case_id} with steps: {', '.join(step_numbers)}")

        # Start with the first step's script
        combined_script = state.previous_scripts[step_numbers[0]]

        # Add a header comment to the combined script
        header = f"""# Combined Test Script for Test Case: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Includes steps: {', '.join(step_numbers)}
"""
        combined_script = header + combined_script

        # Merge each subsequent step's script
        for i in range(1, len(step_numbers)):
            current_step = step_numbers[i]
            current_script = state.previous_scripts[current_step]

            # Use AI-powered merge
            logger.info(f"Merging step {current_step} into combined script using AI")
            combined_script = merge_scripts_with_ai(combined_script, current_script, api_key=state.google_api_key)

        # Create a file path for the combined script
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)
        combined_script_file = os.path.join(
            script_dir,
            f"test_{test_case_id}_combined_{int(time.time())}.py"
        )

        # Save the combined script to a file
        with open(combined_script_file, "w") as f:
            f.write(combined_script)

        # Store the combined script content and path in the state for Stage 8
        state.combined_script_content = combined_script
        state.combined_script_path = combined_script_file
        logger.info(f"Stored combined script content in state for Stage 8 (length: {len(combined_script)} chars)")
        logger.info(f"Stored combined script path in state: {combined_script_file}")

        logger.info(f"Successfully created combined script file: {combined_script_file}")
        return combined_script_file

    except Exception as e:
        logger.error(f"Error creating combined script: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

# Import helper functions from other modules
from core.element_matching import match_elements_with_ai
from core.helpers_diff import generate_annotated_script
from helpers_pure import analyze_step_for_test_data

def advance_to_next_step():
    """
    Advance to the next test case step.

    This function:
    1. Increments the current step index
    2. Selects the next test case step from the step table
    3. Updates state manager variables
    4. Returns True if there are more steps, False if all steps are processed

    Returns:
        bool: True if successfully advanced to the next test case step, False if all steps are processed
    """
    logger.info("=== advance_to_next_step() called ===")

    # Get state manager
    from state_manager import StateManager
    state = StateManager.get(st)

    # Check if we have a step table
    if not state.step_table_json or not isinstance(state.step_table_json, list):
        logger.warning("No valid step_table_json found")
        return False

    # Get the total number of steps if not already set
    if state.total_steps == 0:
        state.total_steps = len(state.step_table_json)
        logger.info(f"State change: total_steps = {state.total_steps}")

    # Log detailed state information before advancing
    logger.info(f"State before advancing: current_step_index = {state.current_step_index}")
    logger.info(f"State before advancing: total_steps = {state.total_steps}")
    logger.info(f"State before advancing: step_ready_for_script = {state.step_ready_for_script}")
    logger.info(f"State before advancing: all_steps_done = {state.all_steps_done}")

    # Also log to Streamlit session state for debugging
    st.session_state['debug_before_advance'] = {
        'current_step_index': state.current_step_index,
        'total_steps': state.total_steps,
        'step_ready_for_script': state.step_ready_for_script,
        'all_steps_done': state.all_steps_done,
        'timestamp': datetime.now().strftime("%H:%M:%S.%f")
    }

    # Save the current step information before advancing
    current_step_info = None
    if hasattr(state, 'selected_step') and state.selected_step:
        current_step_info = {
            'step_no': state.selected_step.get('Step No', 'Unknown'),
            'test_steps': state.selected_step.get('Test Steps', ''),
            'expected_result': state.selected_step.get('Expected Result', '')
        }
        logger.info(f"Current step before advancing: {current_step_info['step_no']}")

    # Use the state manager's update method to increment the step index
    new_step_index = state.current_step_index + 1
    state.update_step_progress(current_step_index=new_step_index)

    # Check if we've processed all steps
    if new_step_index >= state.total_steps:
        logger.info("All steps processed, setting all_steps_done to True")
        # Set all_steps_done to True only if we're actually on the last step
        # This ensures we don't prematurely mark all steps as done
        if state.current_step_index == state.total_steps - 1:
            state.update_step_progress(all_steps_done=True)
            logger.info(f"Setting all_steps_done=True on last step (index {state.current_step_index}, total {state.total_steps})")
        else:
            logger.warning(f"Not setting all_steps_done=True because we're not on the last step (index {state.current_step_index}, total {state.total_steps})")

        # Add a message to session state for display after rerun
        st.session_state['stage_progression_message'] = "✅ All test case steps have been processed!"

        # Check if we should automatically transition to Stage 3
        # This is controlled by the user preference set in Stage 7
        auto_transition_to_stage3 = getattr(state, 'auto_transition_to_stage3', False)

        # If auto-transition is enabled, set the flag to transition to Stage 3
        if auto_transition_to_stage3:
            logger.info("Auto-transitioning to Stage 3 after completing all steps")
            # Reset test case state with confirmation
            state.reset_test_case_state(confirm=True, reason="All steps completed, auto-transitioning to Stage 3")

            # Set a flag to indicate we're transitioning from Stage 7 to Stage 3
            st.session_state['transitioning_to_stage3'] = True
            st.session_state['stage_progression_message'] = "✅ All steps completed. Automatically returning to Stage 3 to select a new test case."

        # Force state update in session state
        st.session_state['state'] = state

        # Immediately rerun to refresh the UI with the new state
        st.rerun()
        # Return statement will never be reached due to rerun, but included for clarity
        return False

    # Get the next step from the step table
    next_step = state.step_table_json[state.current_step_index]
    logger.info(f"Next step: step_no = {next_step.get('step_no')}, action = {next_step.get('action')}")

    # Find the corresponding original step
    if state.selected_test_case and 'Steps' in state.selected_test_case:
        original_steps = state.selected_test_case.get('Steps', [])
        step_no = str(next_step.get('step_no', ''))
        logger.info(f"Looking for original step with Step No: {step_no}")

        try:
            selected_original_step = next(
                (step for step in original_steps if str(step.get('Step No')) == step_no),
                None
            )

            if selected_original_step:
                logger.info(f"Found original step: Step No = {selected_original_step.get('Step No')}")
            else:
                logger.warning(f"Original step not found for step_no: {step_no}")
                logger.warning(f"Available steps: {[str(step.get('Step No')) for step in original_steps]}")
        except Exception as e:
            logger.error(f"Error finding original step: {e}")
            selected_original_step = None

        if selected_original_step and next_step:
            # Save context from the current step before updating state
            if current_step_info:
                # Mark the current step as completed
                current_step_no = current_step_info['step_no']

                # Initialize completed_steps if not present
                if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                    state.completed_steps = []

                # Add current step to completed steps if not already there
                if current_step_no not in state.completed_steps:
                    state.completed_steps.append(current_step_no)
                    logger.info(f"State change: added step {current_step_no} to completed_steps")

                # Store context from the current step that might be useful for the next step
                if not hasattr(state, 'step_context') or not isinstance(state.step_context, dict):
                    state.step_context = {}

                # Save relevant context from the current step
                state.step_context[current_step_no] = {
                    "elements": state.step_elements if hasattr(state, 'step_elements') else [],
                    "matches": state.step_matches if hasattr(state, 'step_matches') else {},
                    "test_data": state.test_data if hasattr(state, 'test_data') else {},
                    "script_path": state.generated_script_path if hasattr(state, 'generated_script_path') else None
                }
                # Log state change for context saving
                previous_context = state.step_context.get(current_step_no, None)
                if previous_context != state.step_context[current_step_no]:
                    logger.info(f"State change: saved context for step {current_step_no}")

            # Update state manager with the new step
            state.selected_step_table_entry = next_step
            state.selected_step = selected_original_step
            # Log state change for step selection
            previous_step_no = state.selected_step.get('Step No') if hasattr(state, 'selected_step') and state.selected_step else None
            new_step_no = selected_original_step.get('Step No')
            if previous_step_no != new_step_no:
                logger.info(f"State change: updated selected_step_table_entry and selected_step to Step No {new_step_no}")

            # Reset step-specific state variables
            state.step_elements = []
            state.step_matches = {}
            state.test_data = {}
            state.test_data_skipped = False
            state.llm_step_analysis = {}
            state.step_ready_for_script = False
            state.script_just_generated = False
            # Only log if there were actual changes to reset
            if (hasattr(state, 'step_elements') and state.step_elements) or \
               (hasattr(state, 'step_matches') and state.step_matches) or \
               (hasattr(state, 'test_data') and state.test_data) or \
               (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or \
               (hasattr(state, 'llm_step_analysis') and state.llm_step_analysis) or \
               (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or \
               (hasattr(state, 'script_just_generated') and state.script_just_generated):
                logger.info("State change: reset step-specific state variables")

            # Log detailed state information after advancing
            logger.info(f"State after advancing: current_step_index = {state.current_step_index}")
            logger.info(f"State after advancing: total_steps = {state.total_steps}")
            logger.info(f"State after advancing: step_ready_for_script = {state.step_ready_for_script}")
            logger.info(f"State after advancing: all_steps_done = {state.all_steps_done}")
            logger.info(f"State after advancing: selected_step.Step No = {state.selected_step.get('Step No')}")

            # Also log to Streamlit session state for debugging
            st.session_state['debug_after_advance'] = {
                'current_step_index': state.current_step_index,
                'total_steps': state.total_steps,
                'step_ready_for_script': state.step_ready_for_script,
                'all_steps_done': state.all_steps_done,
                'selected_step_no': state.selected_step.get('Step No'),
                'timestamp': datetime.now().strftime("%H:%M:%S.%f")
            }

            # Add a message to session state for display after rerun
            next_step_no = selected_original_step.get('Step No')
            next_step_action = selected_original_step.get('Test Steps', '')[:50]  # Truncate for display
            st.session_state['stage_progression_message'] = f"✅ Advanced to Test Case Step {next_step_no}: {next_step_action}..."

            # Force the state to be updated in the session state
            st.session_state['state'] = state

            logger.info(f"Successfully advanced to next step: {next_step_no}")
            # Immediately rerun to refresh the UI with the new state
            st.rerun()
            # Return statement will never be reached due to rerun, but included for clarity
            return True
        else:
            logger.warning(f"Failed to advance: selected_original_step={selected_original_step is not None}, next_step={next_step is not None}")
    else:
        logger.warning("No selected_test_case or no Steps in selected_test_case")

    logger.warning("Failed to advance to next step")
    return False



# Workflow progress is now exclusively displayed in the sidebar















def stage7_run_script(state):
    """
    Phase 7: Run Test Script for Selected Test Case Step.

    This stage allows the user to run the generated test script for the selected test case step.
    It checks if a script has been generated, displays the script, and provides a simple button
    to run the test. When the user clicks the button, it executes the script using pytest and
    displays the results, including any screenshots captured during the test run.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 7: Run Test Script</h2>", unsafe_allow_html=True)

    if not hasattr(state, 'generated_script_path') or not state.generated_script_path:
        st.warning("⚠️ Please generate a test script in Phase 6 first")
        return

    # Create two columns for script info and test status
    col1, col2 = st.columns([3, 2])

    with col1:
        st.success(f"✓ Script ready: {os.path.basename(state.generated_script_path)}")

    with col2:
        # Show test case step info
        step_no = state.selected_step.get('Step No', 'Unknown')
        st.info(f"Test Case Step: {step_no}")

    # Add user preference for auto-transitioning to Stage 3 after completing all steps
    # This is only shown when we're on the last step
    if state.current_step_index == state.total_steps - 1:
        st.info("📌 This is the last step in the test case.")
        # Get default value from state if available
        default_auto_transition = getattr(state, 'auto_transition_to_stage3', False)
        auto_transition = st.checkbox(
            "Automatically return to Phase 3 after completing all steps",
            value=default_auto_transition,
            help="If checked, the application will automatically return to Phase 3 to select a new test case after completing all steps"
        )
        # Store the preference in state
        state.auto_transition_to_stage3 = auto_transition
        logger.info(f"State change: auto_transition_to_stage3 = {auto_transition}")

    # Display the generated script in a collapsible section
    if hasattr(state, 'last_script_content') and state.last_script_content:
        with st.expander("View Test Script", expanded=False):
            st.code(state.last_script_content, language="python")
            if hasattr(state, 'last_script_file') and state.last_script_file:
                st.info(f"Script file: {state.last_script_file}")

    # Add a visual indicator to encourage running the script
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Click the button below to run the test script</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">This will open a browser window to execute the test</p>
    </div>
    """, unsafe_allow_html=True)

    # Simplified run button without options
    if st.button("Run Test Script", disabled=not state.generated_script_path, use_container_width=True):
        with st.spinner(f"Running test script for Step {state.selected_step.get('Step No')}..."):
            try:
                # Set environment variables for the test run
                env = os.environ.copy()
                # Always run in visible mode (not headless)
                env["HEADLESS"] = "0"

                # Run the test script
                import subprocess
                result = subprocess.run(
                    ["pytest", state.generated_script_path, "--tb=short", "-v"],
                    capture_output=True, text=True,
                    env=env
                )

                # Store the test results
                state.test_results = {
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode,
                    "step_no": state.selected_step.get('Step No'),
                    "test_case_id": state.selected_test_case.get('Test Case ID')
                }

                # Display the test results
                if result.returncode == 0:
                    st.success(f"Test Case Step {state.selected_step.get('Step No')} test passed!")

                    # Set the step_ready_for_script flag to True after successful test execution
                    if not state.step_ready_for_script:
                        state.step_ready_for_script = True
                        logger.info("State change: step_ready_for_script = True (after successful test execution)")

                    # Force state update in session state
                    st.session_state['state'] = state
                else:
                    st.error(f"Test Case Step {state.selected_step.get('Step No')} test failed. See details below.")

                with st.expander("Test Results", expanded=True):
                    st.code(result.stdout)
                    if result.stderr:
                        st.error("Errors:")
                        st.code(result.stderr)

                # Check for screenshots
                screenshots_dir = Path("screenshots")
                if screenshots_dir.exists():
                    screenshots = list(screenshots_dir.glob(f"*step{state.selected_step.get('Step No')}*.png"))
                    if screenshots:
                        st.subheader("Test Screenshots")
                        for screenshot in screenshots:
                            st.image(str(screenshot), caption=screenshot.name)

                # Set flags to indicate the current step is completed and ready for next step
                # Check if there are more steps
                next_step_index = state.current_step_index + 1
                if next_step_index < state.total_steps:
                        # Get the next step information
                        next_step = state.step_table_json[next_step_index]
                        next_step_no = next_step.get('step_no', 'N/A')

                        # Mark Stage 7 as complete
                        st.success(f"✅ Test Case Step {state.selected_step.get('Step No')} completed successfully!")
                        st.info("Automatically advancing to the next test case step...")

                        # Add a small delay to allow the user to see the success message
                        time.sleep(1.5)

                        # Store current step completion status
                        current_step_no = state.selected_step.get('Step No')
                        if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                            state.completed_steps = []
                        if current_step_no not in state.completed_steps:
                            state.completed_steps.append(current_step_no)
                            logger.info(f"State change: added step {current_step_no} to completed_steps")

                        # The flag for script generation should already be set to True after test execution
                        # Double-check to make sure it's set
                        if not state.step_ready_for_script:
                            state.step_ready_for_script = True
                            logger.info("State change: step_ready_for_script = True (before automatic advancement)")

                        # Log the automatic advancement
                        logger.info(f"Automatically advancing to next step after test execution: {next_step_no}")

                        # Set up session state variables before calling advance_to_next_step
                        # since it will trigger a rerun if successful

                        # Set a message to be displayed in Stage 4
                        st.session_state['stage_progression_message'] = f"✅ Test Case Step {current_step_no} completed and automatically advanced to Step {next_step_no}"

                        # Add debug information to track the state update
                        st.session_state['auto_advance_debug'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'from_step': current_step_no,
                            'to_step': next_step_no,
                            'current_step_index': state.current_step_index
                        }

                        # Set a flag to force a refresh after advancement
                        st.session_state['force_refresh_after_advance'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'target_step': next_step_no,
                            'from_step': current_step_no
                        }

                        # Set a flag to indicate we're coming back from Stage 7
                        # This will be used in run_app() to stop at Stage 4
                        st.session_state['coming_from_stage7'] = True
                        logger.info(f"Setting coming_from_stage7 flag to return to Stage 4 with step {next_step_no}")

                        # Force state update in session state
                        st.session_state['state'] = state

                        # Add a more visible indicator that we're advancing
                        st.success("🔄 Advancing to the next step... Please wait.")

                        # Add a small delay to ensure the state is properly updated
                        time.sleep(0.5)

                        # Call advance_to_next_step to move to the next step
                        # This will call st.rerun() internally if successful
                        advance_to_next_step()

                        # The code below will only execute if advance_to_next_step() fails
                        # and doesn't trigger a rerun
                        st.error("Failed to automatically advance to the next step. Please check the logs.")
                        logger.error("Failed to automatically advance to the next step after test execution")
                else:
                    # All steps processed
                    state.all_steps_done = True
                    st.success("✅ All test case steps have been processed!")

                    # Create a combined script file for the entire test case
                    combined_script_path = create_combined_script(state)
                    if combined_script_path:
                        # Ensure the combined_script_path is set in the state
                        state.combined_script_path = combined_script_path
                        logger.info(f"Stage 7: Set combined_script_path in state to {combined_script_path}")

                        st.success(f"📄 Created combined script file: {os.path.basename(combined_script_path)}")
                        with st.expander("View Combined Script", expanded=False):
                            with open(combined_script_path, 'r') as f:
                                combined_script_content = f.read()
                            st.code(combined_script_content, language="python")
                            st.info(f"Combined script file: {combined_script_path}")

                    # Add a button to proceed to Script Optimization (Stage 8)
                    st.markdown("""
                    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
                        <p style="font-size: 16px; color: #4CAF50; margin: 0;">All steps completed for this test case</p>
                        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Click the button below to proceed to Script Optimization (Phase 8)</p>
                    </div>
                    """, unsafe_allow_html=True)

                    if st.button("Proceed to Script Optimization (Phase 8)", use_container_width=True):
                        # Set a flag to indicate we're transitioning from Stage 7 to Stage 8
                        st.session_state['transitioning_to_stage8'] = True
                        st.session_state['stage_progression_message'] = "✅ All steps completed. Proceeding to Script Optimization (Phase 8)."

                        # Force state update in session state
                        st.session_state['state'] = state
                        st.rerun()
            except Exception as e:
                st.error(f"Error running test script: {e}")
                import traceback
                st.error(traceback.format_exc())

def stage8_optimize_script(state):
    """
    Phase 8: Script Consolidation and Optimization.

    This stage takes the combined script from Phase 7 and optimizes it using Google AI
    to create a well-structured, cohesive PyTest module following best practices.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Phase 8: Script Optimization</h2>", unsafe_allow_html=True)

    # Check if prerequisites are met
    if (not hasattr(state, 'combined_script_content') or not state.combined_script_content or
        not hasattr(state, 'combined_script_path') or not state.combined_script_path):
        logger.info("Stage 8: Missing combined script content or path in state, attempting to load or create it")

        # Check if we have a combined_script_path but no content
        if hasattr(state, 'combined_script_path') and state.combined_script_path:
            logger.info(f"Stage 8: Found combined_script_path in state: {state.combined_script_path}")
            try:
                with open(state.combined_script_path, 'r') as f:
                    state.combined_script_content = f.read()
                logger.info(f"Stage 8: Successfully loaded combined script from existing path: {state.combined_script_path} ({len(state.combined_script_content)} characters)")
                st.success(f"✓ Loaded combined script from: {os.path.basename(state.combined_script_path)}")
            except Exception as e:
                error_msg = f"Error reading combined script file from path: {str(e)}"
                logger.error(f"Stage 8: {error_msg}")
                st.error(error_msg)
                # Continue to try creating a new combined script

        # Try to create a new combined script if needed
        if (not hasattr(state, 'combined_script_content') or not state.combined_script_content or
            not hasattr(state, 'combined_script_path') or not state.combined_script_path):
            # Try to load the combined script from the file if available
            if hasattr(state, 'previous_scripts') and state.previous_scripts:
                logger.info(f"Stage 8: Found {len(state.previous_scripts)} previous scripts, creating combined script")
                # Create a combined script file for the entire test case
                combined_script_path = create_combined_script(state)
                if combined_script_path:
                    try:
                        # The create_combined_script function should have already set these in the state,
                        # but let's double-check to be sure
                        if not hasattr(state, 'combined_script_path') or not state.combined_script_path:
                            state.combined_script_path = combined_script_path
                            logger.info(f"Stage 8: Set combined_script_path in state to {combined_script_path}")

                        if not hasattr(state, 'combined_script_content') or not state.combined_script_content:
                            with open(combined_script_path, 'r') as f:
                                state.combined_script_content = f.read()
                            logger.info(f"Stage 8: Set combined_script_content in state ({len(state.combined_script_content)} characters)")

                        logger.info(f"Stage 8: Successfully loaded combined script from: {combined_script_path}")
                        st.success(f"✓ Loaded combined script from: {os.path.basename(combined_script_path)}")
                    except Exception as e:
                        error_msg = f"Error reading combined script file: {str(e)}"
                        logger.error(f"Stage 8: {error_msg}")
                        st.error(error_msg)
                        return
                else:
                    logger.warning("Phase 8: Failed to create combined script, redirecting user to Phase 7")
                    st.warning("⚠️ Please complete Phase 7 first to generate a combined script")
                    return
            else:
                logger.warning("Phase 8: No previous scripts found, redirecting user to Phase 7")
                st.warning("⚠️ Please complete Phase 7 first to generate a combined script")
                return

    # Display the combined script content in an expander
    with st.expander("View Combined Script", expanded=False):
        st.code(state.combined_script_content, language="python")

    # Check if optimization is already complete
    if state.optimization_complete and state.optimized_script_path:
        st.success(f"✓ Script optimization complete: {os.path.basename(state.optimized_script_path)}")
        logger.info(f"Stage 8: Displaying completed optimization results for {state.optimized_script_path}")

        # Show optimization summary
        try:
            with open(state.optimized_script_path, 'r') as f:
                optimized_script_content = f.read()

            # Calculate statistics for the optimized script
            original_script = state.combined_script_content
            original_lines = original_script.count('\n') + 1
            original_chars = len(original_script)

            optimized_lines = optimized_script_content.count('\n') + 1
            optimized_chars = len(optimized_script_content)

            lines_diff = optimized_lines - original_lines
            chars_diff = optimized_chars - original_chars

            lines_percent = (lines_diff / original_lines) * 100 if original_lines > 0 else 0
            chars_percent = (chars_diff / original_chars) * 100 if original_chars > 0 else 0

            # Display optimization metrics
            st.markdown("#### Optimization Results")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Original Lines", f"{original_lines:,}",
                          delta=f"{lines_diff:+,} ({lines_percent:.1f}%)",
                          delta_color="inverse")

            with col2:
                st.metric("Optimized Lines", f"{optimized_lines:,}")

            with col3:
                if hasattr(state, 'optimization_start_time') and state.optimization_start_time:
                    optimization_duration = datetime.now() - state.optimization_start_time
                    duration_seconds = optimization_duration.total_seconds()
                    st.metric("Processing Time", f"{duration_seconds:.1f}s")

            # Count imports, fixtures, and test functions in both scripts
            import re
            original_imports = len(re.findall(r'^import |^from ', original_script, re.MULTILINE))
            original_fixtures = len(re.findall(r'@pytest\.fixture', original_script, re.MULTILINE))
            original_tests = len(re.findall(r'def test_', original_script, re.MULTILINE))

            optimized_imports = len(re.findall(r'^import |^from ', optimized_script_content, re.MULTILINE))
            optimized_fixtures = len(re.findall(r'@pytest\.fixture', optimized_script_content, re.MULTILINE))
            optimized_tests = len(re.findall(r'def test_', optimized_script_content, re.MULTILINE))

            # Display detailed metrics
            st.markdown("#### Code Structure Comparison")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("Imports", f"{optimized_imports}",
                          delta=f"{optimized_imports - original_imports:+}")

            with col2:
                st.metric("Fixtures", f"{optimized_fixtures}",
                          delta=f"{optimized_fixtures - original_fixtures:+}")

            with col3:
                st.metric("Test Functions", f"{optimized_tests}",
                          delta=f"{optimized_tests - original_tests:+}")

            logger.info(f"Stage 8: Optimization metrics - Original: {original_lines} lines, {original_chars} chars; " +
                       f"Optimized: {optimized_lines} lines, {optimized_chars} chars; " +
                       f"Diff: {lines_diff} lines ({lines_percent:.1f}%), {chars_diff} chars ({chars_percent:.1f}%)")

            # Display the optimized script in an expander
            with st.expander("View Optimized Script", expanded=True):
                st.code(optimized_script_content, language="python")
                st.info(f"Script file: {state.optimized_script_path}")

            # Add a download button for the optimized script
            st.download_button(
                label="Download Optimized Script",
                data=optimized_script_content,
                file_name=os.path.basename(state.optimized_script_path),
                mime="text/plain",
                key="download_optimized_script"
            )

            # Option to view the original script for comparison
            with st.expander("View Original Script (Before Optimization)", expanded=False):
                st.code(original_script, language="python")

        except Exception as e:
            error_msg = f"Error displaying optimization results: {str(e)}"
            st.error(error_msg)
            logger.error(f"Stage 8: {error_msg}", exc_info=True)

        # Add a button to return to Phase 3 to select a new test case
        st.markdown("""
        <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
            <p style="font-size: 16px; color: #4CAF50; margin: 0;">Script optimization complete</p>
            <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Click the button below to return to Phase 3 and select a new test case</p>
        </div>
        """, unsafe_allow_html=True)

        if st.button("Return to Test Case Selection (Phase 3)", use_container_width=True):
            logger.info("Phase 8: User clicked 'Return to Test Case Selection' button")

            # Reset test case state with confirmation
            state.reset_test_case_state(confirm=True, reason="Script optimization complete, returning to Phase 3")
            logger.info("Phase 8: Reset test case state to prepare for new test case")

            # Set a flag to indicate we're transitioning from Phase 8 to Phase 3
            st.session_state['transitioning_from_stage8'] = True
            st.session_state['stage_progression_message'] = "✅ Script optimization complete. Returning to Phase 3 to select a new test case."
            logger.info("Phase 8: Set transitioning_from_stage8 flag for workflow transition")

            # Force state update in session state
            st.session_state['state'] = state
            logger.info("Phase 8: Rerunning app to transition to Phase 3")
            st.rerun()

        return

    # If optimization is in progress, show a spinner
    if state.optimization_in_progress:
        with st.spinner("Optimizing script... This may take a minute or two."):
            try:
                # Get the combined script content
                combined_script = state.combined_script_content
                logger.info(f"Stage 8: Starting script optimization process for script with {len(combined_script)} characters")

                # Initialize optimization start time if not already set
                if not hasattr(state, 'optimization_start_time') or not state.optimization_start_time:
                    state.optimization_start_time = datetime.now()
                    logger.info(f"Stage 8: Setting optimization start time to {state.optimization_start_time}")

                # Check if the script is too large for a single optimization
                if len(combined_script) > 30000:  # 30k character limit for safety
                    st.info("Script is large, using chunked optimization approach...")
                    logger.info("Stage 8: Script exceeds 30k characters, using chunked optimization approach")

                    # Split the script into logical sections
                    # This is a simple approach - in a real implementation, you'd want more sophisticated parsing

                    # Extract imports section (all lines starting with import or from)
                    imports_pattern = r'((?:^import .*$|^from .*$)(?:\n|$))+'
                    imports_match = re.search(imports_pattern, combined_script, re.MULTILINE)
                    imports_section = imports_match.group(0) if imports_match else ""
                    logger.info(f"Stage 8: Extracted imports section ({len(imports_section)} characters)")

                    # Extract fixtures section (all @pytest.fixture blocks)
                    fixtures_pattern = r'(@pytest\.fixture.*?def .*?\):(?:\n    .*?)*?)(?=\n\n)'
                    fixtures_matches = re.finditer(fixtures_pattern, combined_script, re.DOTALL)
                    fixtures_section = "\n\n".join([m.group(0) for m in fixtures_matches]) if fixtures_matches else ""
                    logger.info(f"Stage 8: Extracted fixtures section ({len(fixtures_section)} characters)")

                    # Extract test functions (all def test_* blocks)
                    test_pattern = r'(def test_.*?\):(?:\n    .*?)*?)(?=\n\n|$)'
                    test_matches = re.finditer(test_pattern, combined_script, re.DOTALL)
                    test_functions_section = "\n\n".join([m.group(0) for m in test_matches]) if test_matches else ""
                    logger.info(f"Stage 8: Extracted test functions section ({len(test_functions_section)} characters)")

                    # Check if we successfully extracted all sections
                    if not imports_section and not fixtures_section and not test_functions_section:
                        logger.warning("Stage 8: Failed to extract any sections from the script, falling back to full script optimization")
                        st.warning("Could not split script into sections, attempting full optimization...")
                        # Fall back to optimizing the entire script
                        optimized_script = optimize_script_with_ai(
                            combined_script,
                            api_key=state.google_api_key
                        )
                    else:
                        # Optimize each section separately
                        logger.info("Stage 8: Optimizing imports section")
                        optimized_imports = optimize_script_with_ai(
                            imports_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 1, 'total_chunks': 3, 'chunk_type': 'imports'}
                        )
                        logger.info(f"Stage 8: Imports section optimization complete ({len(optimized_imports)} characters)")

                        logger.info("Stage 8: Optimizing fixtures section")
                        optimized_fixtures = optimize_script_with_ai(
                            fixtures_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 2, 'total_chunks': 3, 'chunk_type': 'fixtures'}
                        )
                        logger.info(f"Stage 8: Fixtures section optimization complete ({len(optimized_fixtures)} characters)")

                        logger.info("Stage 8: Optimizing test functions section")
                        optimized_tests = optimize_script_with_ai(
                            test_functions_section,
                            api_key=state.google_api_key,
                            chunk_mode=True,
                            chunk_info={'chunk_number': 3, 'total_chunks': 3, 'chunk_type': 'test_functions'}
                        )
                        logger.info(f"Stage 8: Test functions section optimization complete ({len(optimized_tests)} characters)")

                        # Combine the optimized sections
                        optimized_script = f"{optimized_imports}\n\n{optimized_fixtures}\n\n{optimized_tests}"
                        logger.info(f"Stage 8: Combined optimized sections into final script ({len(optimized_script)} characters)")
                else:
                    # Optimize the entire script at once
                    logger.info("Stage 8: Optimizing entire script in one call")
                    optimized_script = optimize_script_with_ai(
                        combined_script,
                        api_key=state.google_api_key
                    )
                    logger.info(f"Stage 8: Script optimization complete ({len(optimized_script)} characters)")

                # Save the optimized script to a file
                script_dir = "generated_tests"
                os.makedirs(script_dir, exist_ok=True)
                logger.info(f"Stage 8: Ensured output directory exists: {script_dir}")

                # Get the test case ID
                test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

                # Create a file path for the optimized script
                timestamp = int(time.time())
                optimized_script_file = os.path.join(
                    script_dir,
                    f"test_{test_case_id}_optimized_{timestamp}.py"
                )
                logger.info(f"Stage 8: Created output file path: {optimized_script_file}")

                # Save the optimized script to a file
                try:
                    with open(optimized_script_file, "w") as f:
                        f.write(optimized_script)
                    logger.info(f"Stage 8: Successfully saved optimized script to {optimized_script_file}")
                except Exception as e:
                    error_msg = f"Error saving optimized script: {str(e)}"
                    logger.error(f"Stage 8: {error_msg}")
                    st.error(error_msg)
                    state.optimization_in_progress = False
                    st.session_state['state'] = state
                    st.rerun()
                    return

                # Calculate optimization duration
                if hasattr(state, 'optimization_start_time') and state.optimization_start_time:
                    optimization_duration = datetime.now() - state.optimization_start_time
                    logger.info(f"Stage 8: Optimization completed in {optimization_duration.total_seconds():.2f} seconds")

                # Update the state
                state.optimized_script_path = optimized_script_file
                state.optimized_script_content = optimized_script
                state.optimization_complete = True
                state.optimization_in_progress = False
                logger.info("Stage 8: Updated state with optimization results and set optimization_complete=True")

                # Force state update in session state
                st.session_state['state'] = state
                logger.info("Stage 8: Rerunning app to display optimization results")
                st.rerun()

            except Exception as e:
                error_msg = f"Error optimizing script: {str(e)}"
                st.error(error_msg)
                logger.error(f"Stage 8: {error_msg}", exc_info=True)
                import traceback
                st.error(traceback.format_exc())
                logger.error(f"Stage 8: Detailed error traceback: {traceback.format_exc()}")

                # Reset the optimization state
                state.optimization_in_progress = False
                logger.info("Stage 8: Reset optimization_in_progress to False after error")

                # Force state update in session state
                st.session_state['state'] = state
                logger.info("Stage 8: Rerunning app after error")
                st.rerun()

    # If optimization is not in progress and not complete, show the optimization options
    st.markdown("#### Optimization Options")

    # Display information about the optimization process
    with st.expander("About Script Optimization", expanded=False):
        st.info("""
        This stage will optimize the combined script using Google AI to create a well-structured,
        cohesive PyTest module following best practices. The optimization process will:

        1. Refactor the script into a well-structured PyTest module
        2. Ensure proper test setup and teardown with appropriate fixtures
        3. Remove redundancies and optimize the code
        4. Maintain all functionality from the original steps
        5. Follow best practices for PyTest automation
        6. Add clear comments explaining the optimization process
        7. Preserve all imports, assertions, and test logic
        """)

        # Add more detailed information about what happens during optimization
        st.markdown("""
        #### What happens during optimization?

        The optimization process analyzes your combined script and makes improvements in several areas:

        - **Code Structure**: Organizes imports, fixtures, and test functions in a logical way
        - **Redundancy Removal**: Eliminates duplicate code and consolidates similar operations
        - **Error Handling**: Improves exception handling and adds appropriate try/except blocks
        - **Documentation**: Adds clear comments and docstrings to explain the test flow
        - **Best Practices**: Applies PyTest best practices for maintainable test automation

        The original functionality is preserved while making the code more efficient and easier to maintain.
        """)

    # Display script statistics
    if hasattr(state, 'combined_script_content') and state.combined_script_content:
        combined_script = state.combined_script_content
        line_count = combined_script.count('\n') + 1
        char_count = len(combined_script)

        # Count imports, fixtures, and test functions
        import re
        import_count = len(re.findall(r'^import |^from ', combined_script, re.MULTILINE))
        fixture_count = len(re.findall(r'@pytest\.fixture', combined_script, re.MULTILINE))
        test_count = len(re.findall(r'def test_', combined_script, re.MULTILINE))

        st.markdown("#### Script Statistics")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Lines", f"{line_count:,}")
        with col2:
            st.metric("Imports", f"{import_count}")
        with col3:
            st.metric("Fixtures", f"{fixture_count}")
        with col4:
            st.metric("Test Functions", f"{test_count}")

        logger.info(f"Stage 8: Script statistics - Lines: {line_count}, Chars: {char_count}, Imports: {import_count}, Fixtures: {fixture_count}, Tests: {test_count}")

    # Add a button to start the optimization with clear visual emphasis
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Ready to optimize your test script?</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Click the button below to start the optimization process</p>
    </div>
    """, unsafe_allow_html=True)

    if st.button("Start Script Optimization", use_container_width=True, key="start_optimization_button"):
        logger.info("Phase 8: User clicked 'Start Script Optimization' button")

        # Initialize optimization start time
        state.optimization_start_time = datetime.now()
        logger.info(f"Phase 8: Setting optimization start time to {state.optimization_start_time}")

        # Set the optimization in progress flag
        state.optimization_in_progress = True
        logger.info("Phase 8: Set optimization_in_progress flag to True")

        # Force state update in session state
        st.session_state['state'] = state
        logger.info("Phase 8: Rerunning app to start optimization process")
        st.rerun()
